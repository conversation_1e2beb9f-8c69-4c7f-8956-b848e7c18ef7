/**
 * Real-time Trading Hook
 * Integrates market data with trading strategy and execution
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { ProcessedMarketData } from '@/types/dhan';
import { tradingServiceManager, RealTimeTradingService } from '@/lib/real-time-trading';
import { TradingSignal } from '@/lib/strategies/strategy-service';
import { TradingConfiguration, TradingStats } from '@/lib/trading-config';

interface ExecutedTrade {
  tradeId: string;
  orderId: string;
  status: string;
  tradingMode: 'SANDBOX' | 'LIVE';
  isSuccessful: boolean;
  message: string;
}

interface TradeLog {
  id: string;
  order_type: 'BUY' | 'SELL';
  instrument_symbol: string;
  instrument_name: string;
  order_price: number;
  quantity: number;
  order_status: 'PENDING' | 'PLACED' | 'EXECUTED' | 'CANCELLED' | 'REJECTED' | 'FAILED';
  dhan_order_id?: string;
  execution_price?: number;
  execution_quantity?: number;
  execution_time?: string;
  trading_mode: 'SANDBOX' | 'LIVE';
  error_message?: string;
  created_at: string;
  correlation_id: string;
}

export interface TradingState {
  isActive: boolean;
  config: TradingConfiguration | null;
  stats: TradingStats | null;
  recentSignals: TradingSignal[];
  recentTrades: TradeLog[];
  loading: boolean;
  error: string | null;
}

export interface UseTradingReturn extends TradingState {
  startTrading: () => void;
  stopTrading: () => void;
  toggleAutoTrade: () => Promise<void>;
  switchTradingMode: (mode: 'SANDBOX' | 'LIVE') => Promise<void>;
  refreshConfig: () => Promise<void>;
  processMarketData: (data: ProcessedMarketData) => void;
}

export function useRealTimeTrading(): UseTradingReturn {
  const { user } = useAuth();
  const [state, setState] = useState<TradingState>({
    isActive: false,
    config: null,
    stats: null,
    recentSignals: [],
    recentTrades: [],
    loading: true,
    error: null
  });

  const tradingServiceRef = useRef<RealTimeTradingService | null>(null);
  const configRefreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * Initialize trading service
   */
  const initializeTradingService = useCallback(() => {
    if (!user?.id) return;

    const service = tradingServiceManager.getService(user.id, {
      onSignalGenerated: (signal: TradingSignal) => {
        console.log('Signal generated:', signal);
        setState(prev => ({
          ...prev,
          recentSignals: [signal, ...prev.recentSignals.slice(0, 9)] // Keep last 10 signals
        }));
      },
      onTradeExecuted: (trade: ExecutedTrade) => {
        console.log('Trade executed:', trade);
        // Refresh config and recent data to get updated trades
        refreshConfig();
        loadRecentData();
      },
      onError: (error: Error) => {
        console.error('Trading service error:', error);
        setState(prev => ({
          ...prev,
          error: error.message
        }));
      }
    });

    tradingServiceRef.current = service;
  }, [user?.id]);

  /**
   * Initialize trading setup for new users
   */
  const initializeTradingSetup = useCallback(async () => {
    if (!user?.id) return false;

    try {
      console.log('Initializing trading setup for user:', user.id);

      const response = await fetch('/api/trading/initialize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: user.id })
      });

      const result = await response.json();

      if (result.success) {
        console.log('Trading setup initialized successfully');
        return true;
      } else {
        console.error('Failed to initialize trading setup:', result.error);
        return false;
      }
    } catch (error) {
      console.error('Error initializing trading setup:', error);
      return false;
    }
  }, [user?.id]);

  /**
   * Load trading configuration
   */
  const loadConfig = useCallback(async () => {
    if (!user?.id) return;

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      const response = await fetch(`/api/trading/config?userId=${user.id}`);
      const result = await response.json();

      if (result.success) {
        setState(prev => ({
          ...prev,
          config: result.data.config,
          stats: result.data.stats,
          loading: false
        }));
      } else {
        // If config not found, try to initialize trading setup
        if (result.error?.includes('not found') || result.error?.includes('42501')) {
          console.log('Config not found, attempting to initialize trading setup');
          const initialized = await initializeTradingSetup();

          if (initialized) {
            // Retry loading config after initialization
            const retryResponse = await fetch(`/api/trading/config?userId=${user.id}`);
            const retryResult = await retryResponse.json();

            if (retryResult.success) {
              setState(prev => ({
                ...prev,
                config: retryResult.data.config,
                stats: retryResult.data.stats,
                loading: false
              }));
              return;
            }
          }
        }

        setState(prev => ({
          ...prev,
          error: result.error,
          loading: false
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load config',
        loading: false
      }));
    }
  }, [user?.id, initializeTradingSetup]);

  /**
   * Load recent signals and trades
   */
  const loadRecentData = useCallback(async () => {
    if (!user?.id) return;

    try {
      // Load recent signals
      const signalsResponse = await fetch(`/api/trading/signals?userId=${user.id}&type=signals&limit=10`);
      const signalsResult = await signalsResponse.json();

      // Load recent trades
      const tradesResponse = await fetch(`/api/trading/signals?userId=${user.id}&type=trades&limit=10`);
      const tradesResult = await tradesResponse.json();

      if (signalsResult.success && tradesResult.success) {
        setState(prev => ({
          ...prev,
          recentSignals: signalsResult.data.signals || [],
          recentTrades: tradesResult.data.trades || []
        }));
      }
    } catch (error) {
      console.error('Error loading recent data:', error);
    }
  }, [user?.id]);

  /**
   * Start trading service
   */
  const startTrading = useCallback(() => {
    if (tradingServiceRef.current) {
      tradingServiceRef.current.start();
      setState(prev => ({ ...prev, isActive: true }));
    }
  }, []);

  /**
   * Stop trading service
   */
  const stopTrading = useCallback(() => {
    if (tradingServiceRef.current) {
      tradingServiceRef.current.stop();
      setState(prev => ({ ...prev, isActive: false }));
    }
  }, []);

  /**
   * Toggle auto-trade setting
   */
  const toggleAutoTrade = useCallback(async () => {
    if (!user?.id) return;

    try {
      const response = await fetch('/api/trading/config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user.id,
          action: 'toggleAutoTrade'
        })
      });

      const result = await response.json();

      if (result.success) {
        setState(prev => ({
          ...prev,
          config: result.data.config,
          stats: result.data.stats
        }));
      } else {
        setState(prev => ({
          ...prev,
          error: result.error
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to toggle auto-trade'
      }));
    }
  }, [user?.id]);

  /**
   * Switch trading mode
   */
  const switchTradingMode = useCallback(async (mode: 'SANDBOX' | 'LIVE') => {
    if (!user?.id) return;

    try {
      const response = await fetch('/api/trading/config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user.id,
          action: 'switchTradingMode',
          value: mode
        })
      });

      const result = await response.json();

      if (result.success) {
        setState(prev => ({
          ...prev,
          config: result.data.config,
          stats: result.data.stats
        }));
      } else {
        setState(prev => ({
          ...prev,
          error: result.error
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to switch trading mode'
      }));
    }
  }, [user?.id]);

  /**
   * Refresh configuration
   */
  const refreshConfig = useCallback(async () => {
    await loadConfig();
  }, [loadConfig]);

  /**
   * Process market data through trading service
   */
  const processMarketData = useCallback((data: ProcessedMarketData) => {
    if (tradingServiceRef.current && state.isActive) {
      tradingServiceRef.current.processMarketData(data);
    }
  }, [state.isActive]);

  // Initialize on mount
  useEffect(() => {
    if (user?.id) {
      initializeTradingService();
      loadConfig();
      loadRecentData();
    }

    // Capture the current ref value for cleanup
    const currentTimeoutRef = configRefreshTimeoutRef.current;

    return () => {
      if (currentTimeoutRef) {
        clearTimeout(currentTimeoutRef);
      }
    };
  }, [user?.id, initializeTradingService, loadConfig, loadRecentData]);

  // Auto-start trading if auto-trade is enabled
  useEffect(() => {
    if (state.config?.autoTradeEnabled && !state.isActive) {
      startTrading();
    } else if (!state.config?.autoTradeEnabled && state.isActive) {
      stopTrading();
    }
  }, [state.config?.autoTradeEnabled, state.isActive, startTrading, stopTrading]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (user?.id) {
        tradingServiceManager.removeService(user.id);
      }
    };
  }, [user?.id]);

  return {
    ...state,
    startTrading,
    stopTrading,
    toggleAutoTrade,
    switchTradingMode,
    refreshConfig,
    processMarketData
  };
}
